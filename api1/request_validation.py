#!/usr/bin/env python3
"""
Request Validation and Transformation Middleware for PropBolt API1
Handles common parameter naming mistakes and provides helpful error messages
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class RequestValidator:
    """Validates and transforms API requests to handle common user mistakes"""
    
    # Define expected parameters for each endpoint
    ENDPOINT_SCHEMAS = {
        '/v2/PropertySearch': {
            'required': [],
            'optional': ['address', 'city', 'state', 'zip_code', 'county', 'property_type', 
                        'bedrooms', 'bathrooms', 'square_feet', 'lot_size', 'year_built',
                        'price_min', 'price_max', 'limit', 'offset'],
            'arrays': ['property_type'],
            'common_mistakes': {
                'addresses': 'address',  # addresses -> address
                'zipcode': 'zip_code',   # zipcode -> zip_code
                'zip': 'zip_code',       # zip -> zip_code
                'sqft': 'square_feet',   # sqft -> square_feet
                'beds': 'bedrooms',      # beds -> bedrooms
                'baths': 'bathrooms',    # baths -> bathrooms
                'built': 'year_built',   # built -> year_built
                'min_price': 'price_min', # min_price -> price_min
                'max_price': 'price_max'  # max_price -> price_max
            }
        },
        '/v2/PropertyDetail': {
            'required': [],
            'optional': ['address', 'city', 'state', 'zip_code', 'property_id'],
            'arrays': [],
            'common_mistakes': {
                'addresses': 'address',
                'zipcode': 'zip_code',
                'zip': 'zip_code',
                'id': 'property_id'
            }
        },
        '/v2/PropertyDetailBulk': {
            'required': ['ids'],
            'optional': [],
            'arrays': ['ids'],
            'common_mistakes': {
                'addresses': 'ids',      # addresses array -> ids array (major mistake!)
                'properties': 'ids',     # properties -> ids
                'property_ids': 'ids'    # property_ids -> ids
            },
            'transformation_notes': {
                'addresses': 'PropertyDetailBulk requires property IDs, not addresses. Use PropertySearch first to get IDs.'
            }
        },
        '/v2/AutoComplete': {
            'required': ['query'],
            'optional': ['limit'],
            'arrays': [],
            'common_mistakes': {
                'address': 'query',      # address -> query (major mistake!)
                'search': 'query',       # search -> query
                'term': 'query',         # term -> query
                'q': 'query'            # q -> query
            }
        },
        '/v2/AddressVerification': {
            'required': ['addresses'],
            'optional': [],
            'arrays': ['addresses'],
            'common_mistakes': {
                'address': 'addresses',  # address -> addresses (needs to be array)
                'locations': 'addresses' # locations -> addresses
            }
        },
        '/v2/PropGPT': {
            'required': ['query'],
            'optional': [],
            'arrays': [],
            'required_headers': ['x-openai-key'],
            'common_mistakes': {
                'question': 'query',     # question -> query
                'prompt': 'query',       # prompt -> query
                'search': 'query'        # search -> query
            }
        },
        '/v2/PropertyComps': {
            'required': [],
            'optional': ['address', 'city', 'state', 'zip_code', 'property_id', 'radius', 'limit'],
            'arrays': [],
            'common_mistakes': {
                'addresses': 'address',
                'zipcode': 'zip_code',
                'zip': 'zip_code',
                'id': 'property_id',
                'distance': 'radius'
            }
        },
        '/v3/PropertyComps': {
            'required': [],
            'optional': ['address', 'city', 'state', 'zip_code', 'property_id', 'radius', 
                        'limit', 'bedrooms', 'bathrooms', 'square_feet', 'property_type'],
            'arrays': [],
            'common_mistakes': {
                'addresses': 'address',
                'zipcode': 'zip_code',
                'zip': 'zip_code',
                'id': 'property_id',
                'distance': 'radius',
                'sqft': 'square_feet',
                'beds': 'bedrooms',
                'baths': 'bathrooms'
            }
        }
    }

    @classmethod
    def validate_and_transform(cls, endpoint: str, data: Dict[str, Any], headers: Dict[str, str]) -> Tuple[Dict[str, Any], List[str], List[str]]:
        """
        Validate and transform request data
        Returns: (transformed_data, warnings, errors)
        """
        if endpoint not in cls.ENDPOINT_SCHEMAS:
            return data, [], [f"Unknown endpoint: {endpoint}"]
        
        schema = cls.ENDPOINT_SCHEMAS[endpoint]
        transformed_data = {}
        warnings = []
        errors = []
        
        # Transform common parameter mistakes
        for key, value in data.items():
            if key in schema.get('common_mistakes', {}):
                correct_key = schema['common_mistakes'][key]
                transformed_data[correct_key] = value
                warnings.append(f"Parameter '{key}' was automatically corrected to '{correct_key}'")
            else:
                transformed_data[key] = value
        
        # Check for required parameters
        for required_param in schema.get('required', []):
            if required_param not in transformed_data:
                errors.append(f"Missing required parameter: '{required_param}'")
        
        # Check for required headers
        for required_header in schema.get('required_headers', []):
            if required_header not in headers:
                errors.append(f"Missing required header: '{required_header}'")
        
        # Validate array parameters
        for array_param in schema.get('arrays', []):
            if array_param in transformed_data:
                if not isinstance(transformed_data[array_param], list):
                    # Try to convert single value to array
                    transformed_data[array_param] = [transformed_data[array_param]]
                    warnings.append(f"Parameter '{array_param}' was converted from single value to array")
        
        # Special validation for specific endpoints
        if endpoint == '/v2/PropertyDetailBulk':
            if 'ids' in transformed_data:
                if not all(isinstance(id_val, (int, str)) for id_val in transformed_data['ids']):
                    errors.append("PropertyDetailBulk 'ids' must be an array of property IDs (numbers or strings)")
        
        return transformed_data, warnings, errors

    @classmethod
    def generate_helpful_error_response(cls, endpoint: str, errors: List[str], warnings: List[str] = None) -> Dict[str, Any]:
        """Generate a helpful error response with suggestions"""
        schema = cls.ENDPOINT_SCHEMAS.get(endpoint, {})
        
        response = {
            'error': 'Request validation failed',
            'endpoint': endpoint,
            'errors': errors,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if warnings:
            response['warnings'] = warnings
        
        # Add helpful suggestions
        suggestions = []
        
        if endpoint in cls.ENDPOINT_SCHEMAS:
            if schema.get('required'):
                suggestions.append(f"Required parameters: {', '.join(schema['required'])}")
            
            if schema.get('optional'):
                suggestions.append(f"Optional parameters: {', '.join(schema['optional'][:5])}{'...' if len(schema['optional']) > 5 else ''}")
            
            if schema.get('arrays'):
                suggestions.append(f"Array parameters (use []): {', '.join(schema['arrays'])}")
            
            if schema.get('required_headers'):
                suggestions.append(f"Required headers: {', '.join(schema['required_headers'])}")
        
        # Add common examples
        examples = cls._get_endpoint_examples(endpoint)
        if examples:
            response['examples'] = examples
        
        if suggestions:
            response['suggestions'] = suggestions
        
        return response

    @classmethod
    def _get_endpoint_examples(cls, endpoint: str) -> Dict[str, Any]:
        """Get example requests for each endpoint"""
        examples = {
            '/v2/PropertySearch': {
                'basic': {'address': '123 Main St', 'city': 'Los Angeles', 'state': 'CA'},
                'with_filters': {'city': 'Austin', 'state': 'TX', 'bedrooms': 3, 'price_max': 500000}
            },
            '/v2/PropertyDetail': {
                'by_address': {'address': '123 Main St', 'city': 'Los Angeles', 'state': 'CA'},
                'by_id': {'property_id': '12345'}
            },
            '/v2/PropertyDetailBulk': {
                'correct': {'ids': [718293, 920392, 811232]},
                'note': 'Use PropertySearch first to get property IDs'
            },
            '/v2/AutoComplete': {
                'correct': {'query': '123 Main'},
                'with_limit': {'query': 'Main St', 'limit': 10}
            },
            '/v2/AddressVerification': {
                'single': {'addresses': ['123 Main St, Los Angeles, CA']},
                'multiple': {'addresses': ['123 Main St, LA, CA', '456 Oak Ave, Austin, TX']}
            },
            '/v2/PropGPT': {
                'example': {'query': 'Find 3 bedroom houses under $400k in Austin'},
                'note': 'Requires x-openai-key header'
            }
        }
        
        return examples.get(endpoint, {})
